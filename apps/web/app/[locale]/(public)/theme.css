
.font-leckerli {
  font-family: "Le<PERSON>li One", cursive;
}

@keyframes pulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }
}

.animated-img {
  z-index: 1;
  cursor: pointer;
  width: 200px;
  height: 200px;
  border-radius: 10px;
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 1s infinite;
  transition: box-shadow 0.3s;
}


/*控制网站标题鼠标移动上去后出现下划线动画*/
.underline-animation {
  position: relative;
}

.underline-animation::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 2px; /* Adjust the thickness of the underline */
  bottom: 0;
  left: 0;
  background-color: currentColor; /* Use the current text color */
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.25s ease-out;
}

.underline-animation:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/*控制语言选项下拉框并两列显示*/
.dropdown-grid ul {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

/* markdown Edtor编辑器样式自定义 */
.dark-editor {
  /* 增强对比度的文本颜色 */
  --mauve-11: #212121; /* 更亮的文本颜色 */
  --accentBase: var(--tomato-1);
  --accentBgSubtle: var(--tomato-2);
  --accentBg: var(--tomato-3);
  --accentBgHover: var(--tomato-4);
  --accentBgActive: var(--tomato-5);
  --accentLine: var(--tomato-6);
  --accentBorder: var(--tomato-7);
  --accentBorderHover: var(--tomato-8);
  --accentSolid: var(--tomato-9);
  --accentSolidHover: var(--tomato-10);
  --accentText: var(--tomato-11);
  --accentTextContrast: var(--tomato-12);

  /* 背景和基础颜色 */
  --baseBase: var(--mauve-1);
  --baseBgSubtle: #1a1a2e; /* 更深的背景色 */
  --baseBg: #16213e; /* 更深的背景色 */
  --baseBgHover: #0f3460; /* 更深的悬停背景色 */
  --baseBgActive: #0f3460; /* 更深的激活背景色 */
  --baseLine: #4a5568; /* 更明显的线条颜色 */
  --baseBorder: #4a5568; /* 更明显的边框颜色 */
  --baseBorderHover: #718096; /* 更明显的悬停边框颜色 */
  --baseSolid: #2d3748; /* 更深的实体颜色 */
  --baseSolidHover: #4a5568; /* 更深的悬停实体颜色 */
  --baseText: #212121; /* 更亮的文本颜色 */
  --baseTextContrast: #ffffff; /* 最高对比度的文本颜色 */

  /* 提示框背景和边框颜色 */
  --admonitionTipBg: rgba(6, 182, 212, 0.2); /* 半透明青色背景 */
  --admonitionTipBorder: rgb(6, 182, 212); /* 实心青色边框 */

  --admonitionInfoBg: rgba(34, 197, 94, 0.2); /* 半透明绿色背景 */
  --admonitionInfoBorder: rgb(34, 197, 94); /* 实心绿色边框 */

  --admonitionCautionBg: rgba(245, 158, 11, 0.2); /* 半透明琥珀色背景 */
  --admonitionCautionBorder: rgb(245, 158, 11); /* 实心琥珀色边框 */

  --admonitionDangerBg: rgba(239, 68, 68, 0.2); /* 半透明红色背景 */
  --admonitionDangerBorder: rgb(239, 68, 68); /* 实心红色边框 */

  --admonitionNoteBg: rgba(148, 163, 184, 0.2); /* 半透明灰色背景 */
  --admonitionNoteBorder: rgb(148, 163, 184); /* 实心灰色边框 */

  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    "Open Sans",
    "Helvetica Neue",
    sans-serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;

  color: var(--baseText);
  --basePageBg: #0f172a; /* 更深的页面背景色 */
  background: var(--basePageBg);
}

.mdxeditor-popup-container {
  z-index: 100 !important;
}

/* 增强MDX编辑器代码块和其他元素的对比度 */
.dark-editor .mdxeditor-code-block {
  background-color: #1e293b !important; /* 深蓝灰色背景 */
  color: var(--baseText) !important; /* 亮色文本 */
  border: 1px solid #334155 !important; /* 深色边框 */
  border-radius: 0.375rem !important;
}

.dark-editor .mdxeditor-code-block pre {
  background-color: transparent !important;
  color: inherit !important;
}

.dark-editor .mdxeditor-code-block code {
  color: #f8fafc !important; /* 亮色代码文本 */
  font-family: var(--font-mono) !important;
}

/* 表格样式增强 */
.dark-editor table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.dark-editor th {
  background-color: #334155 !important; /* 深色表头背景 */
  color: #f8fafc !important; /* 亮色表头文本 */
  font-weight: 600;
  text-align: left;
  padding: 0.75rem;
  border: 1px solid #475569 !important; /* 深色表头边框 */
}

.dark-editor td {
  padding: 0.75rem;
  border: 1px solid #334155 !important; /* 深色单元格边框 */
  color: #f1f5f9 !important; /* 亮色单元格文本 */
}

/* 链接对话框样式 */
.dark-editor .mdxeditor-link-dialog {
  background-color: #1e293b !important; /* 深蓝灰色背景 */
  border: 1px solid #334155 !important; /* 深色边框 */
  border-radius: 0.375rem !important;
  color: #f8fafc !important; /* 亮色文本 */
  padding: 1rem !important;
}

.dark-editor .mdxeditor-link-dialog input {
  background-color: #0f172a !important; /* 更深的输入框背景 */
  color: #f8fafc !important; /* 亮色输入框文本 */
  border: 1px solid #475569 !important; /* 深色输入框边框 */
  border-radius: 0.25rem !important;
  padding: 0.5rem !important;
}

.dark-editor .mdxeditor-link-dialog button {
  background-color: #3b82f6 !important; /* 蓝色按钮背景 */
  color: white !important; /* 白色按钮文本 */
  border-radius: 0.25rem !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
  margin-top: 0.5rem !important;
}

.dark-editor .mdxeditor-link-dialog button:hover {
  background-color: #2563eb !important; /* 深蓝色悬停按钮背景 */
}

/* 修复暗色主题下的光标颜色问题 */
.dark-editor .mdxeditor-root-contenteditable {
  caret-color: var(--baseText) !important;
}

/* 针对具体的编辑元素确保光标可见 */
.dark-editor .mdxeditor-root-contenteditable *,
.dark-editor .mdxeditor-root-contenteditable p,
.dark-editor .mdxeditor-root-contenteditable div,
.dark-editor .mdxeditor-root-contenteditable span {
  caret-color: var(--baseText) !important;
}

/* 确保在聚焦状态下光标可见 */
.dark-editor .mdxeditor-root-contenteditable:focus {
  caret-color: var(--accentText) !important;
  outline: none;
}

/* 确保在聚焦状态下所有子元素的光标可见 */
.dark-editor .mdxeditor-root-contenteditable:focus *,
.dark-editor .mdxeditor-root-contenteditable:focus p,
.dark-editor .mdxeditor-root-contenteditable:focus div,
.dark-editor .mdxeditor-root-contenteditable:focus span {
  caret-color: var(--accentText) !important;
}

/* 暗色主题文本选择颜色 */
.dark-editor .mdxeditor-root-contenteditable::selection {
  background-color: var(--accentSolid) !important;
  color: var(--basePageBg) !important;
}

.dark-editor .mdxeditor-root-contenteditable *::selection {
  background-color: var(--accentSolid) !important;
  color: var(--basePageBg) !important;
}

