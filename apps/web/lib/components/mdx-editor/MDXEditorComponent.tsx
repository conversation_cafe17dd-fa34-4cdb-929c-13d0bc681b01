"use client"
import {
	BoldItalicUnderlineToggles,
	CreateLink,
	DiffSourceToggleWrapper,
	InsertImage,
	InsertTable,
	MDXEditor,
	type MDXEditorMethods,
	type MDXEditorProps,
	UndoRedo,
	codeBlockPlugin,
	codeMirrorPlugin,
	diffSourcePlugin,
	frontmatterPlugin,
	headingsPlugin,
	imagePlugin,
	jsxPlugin,
	linkDialogPlugin,
	linkPlugin,
	listsPlugin,
	markdownShortcutPlugin,
	quotePlugin,
	tablePlugin,
	thematicBreakPlugin,
	toolbarPlugin,
} from "@mdxeditor/editor"
import "@mdxeditor/editor/style.css"
import "./theme.css"
import { cn } from "@lib/utils"
import { useParams } from "next/navigation"
import { toast } from "sonner"
import { putObject } from "@repo/utils/react"
import { forwardRef } from "react"
import dynamic from "next/dynamic"
import { useTranslations } from "next-intl"

// 定义组件接口
interface MDXEditorComponentProps extends MDXEditorProps {
	diffMarkdown?: string
	readOnly?: boolean
	onBlur?: () => void
	variant?: "default" | "paragraph" | "article"
	minHeight?: string
	imageUploadHandler?: (file: File) => Promise<string>
}

// 实际的编辑器组件实现
function MDXEditorComponentImpl({
	diffMarkdown,
	readOnly = false,
	onBlur,
	variant = "default",
	minHeight = "300px",
	imageUploadHandler,
	...props
}: MDXEditorComponentProps & { ref?: React.Ref<MDXEditorMethods> }) {
	const params = useParams()
	const t = useTranslations("MDXEditor")
	const codeBlockLanguages = ["", "txt", "json", "yaml", "toml", "mdx"]

	// 创建翻译函数，兼容 MDXEditor 的 translation prop
	const translateFunction = (
		key: string,
		defaultValue?: string,
		interpolations?: Record<string, any>,
	) => {
		try {
			// 尝试获取翻译
			const translation = t(key as any) || defaultValue || key

			// 如果有插值参数，进行简单的字符串替换
			// if (interpolations && typeof translation === 'string') {
			//   Object.entries(interpolations).forEach(([placeholder, value]) => {
			//     translation = translation.replace(new RegExp(`{{${placeholder}}}`, 'g'), String(value))
			//   })
			// }

			return translation
		} catch (error) {
			// 如果翻译键不存在，返回默认值或键名
			return defaultValue || key
		}
	}

	// 默认的图片上传处理函数
	const defaultImageUploadHandler = async (file: File): Promise<string> => {
		try {
			const projectId = params.project_id as string
			const currentDate = new Date().toISOString().split("T")[0]
			const path = `${projectId}/${currentDate}/images`

			const fileExt = file.name.split(".").pop()
			const fileName = `${path}/${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExt}`
			await putObject(fileName, file)
			return `${process.env.R2_PUBLIC_DOMAIN}/${fileName}`
		} catch (error) {
			console.error("Upload error:", error)
			toast.error("图片上传失败")
			throw error
		}
	}

	// 最小高度现在通过CSS变量在theme.css中控制

	return (
		<MDXEditor
			{...props}
			className="mdx-editor-container"
			translation={translateFunction}
			plugins={[
				headingsPlugin({
					allowedHeadingLevels: [1, 2, 3, 4, 5, 6],
				}),
				diffSourcePlugin({
					diffMarkdown: diffMarkdown,
					readOnlyDiff: true,
				}),
				listsPlugin(),
				quotePlugin(),
				thematicBreakPlugin(),
				frontmatterPlugin(),
				codeBlockPlugin({
					defaultCodeBlockLanguage: "txt",
				}),
				codeMirrorPlugin({
					codeBlockLanguages: codeBlockLanguages.reduce((acc, value) => {
						// @ts-ignore
						acc[value] = value
						return acc
					}, {}),
				}),
				toolbarPlugin({
					toolbarClassName: "mdx-editor-toolbar",
					toolbarContents: () => (
						<>
							<UndoRedo />
							<BoldItalicUnderlineToggles />
							<InsertImage />
							<CreateLink />
							<InsertTable />
							<DiffSourceToggleWrapper>
								<UndoRedo />
							</DiffSourceToggleWrapper>
						</>
					),
				}),
				linkPlugin(),
				linkDialogPlugin(),
				tablePlugin(),
				imagePlugin({
					// 使用传入的处理函数或默认处理函数
					imageUploadHandler: imageUploadHandler || defaultImageUploadHandler,
					imagePreviewHandler: async (url) => {
						return Promise.resolve(url)
					},
				}),
				markdownShortcutPlugin(),
				jsxPlugin(),
			]}
			readOnly={readOnly}
			onBlur={onBlur}
			contentEditableClassName={cn(
				"bg-background prose prose-sm md:prose-base lg:prose-lg w-full max-w-4xl",
				"min-h-[400px] p-4",
				"prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground",
				"prose-a:text-primary hover:prose-a:text-primary/90",
				"prose-code:text-foreground prose-code:bg-muted/50 prose-code:p-1 prose-code:rounded",
				"prose-blockquote:text-foreground/80 prose-blockquote:border-l-4 prose-blockquote:border-primary/50",
				"prose-li:text-foreground",
			)}
		/>
	)
}

// 创建转发ref的组件
const MDXEditorWithRef = forwardRef<MDXEditorMethods, MDXEditorComponentProps>(
	(props, ref) => <MDXEditorComponentImpl {...props} ref={ref} />,
)

// 设置显示名称
MDXEditorWithRef.displayName = "MDXEditorWithRef"

// 使用动态导入避免SSR问题
export const MDXEditorComponent = dynamic(
	() => Promise.resolve(MDXEditorWithRef),
	{
		ssr: false,
	},
)

// 设置显示名称
MDXEditorComponent.displayName = "MDXEditorComponent"
