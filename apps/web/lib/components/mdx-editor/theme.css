/* MDX Editor 自定义主题 */
/* 基于 https://mdxeditor.dev/editor/docs/theming */

/* 编辑器容器 */
.mdx-editor-container {
  --mdx-editor-background: var(--background);
  --mdx-editor-text: var(--foreground);
  --mdx-editor-border: var(--border);
  --mdx-editor-primary: var(--primary);
  --mdx-editor-primary-hover: var(--primary-foreground);
  --mdx-editor-muted: var(--muted);
  --mdx-editor-muted-foreground: var(--muted-foreground);

  /* 覆盖默认样式 */
  border: 1px solid var(--mdx-editor-border);
  border-radius: 0.5rem;
  overflow: hidden;
}

/* 工具栏 */
.mdx-editor-toolbar {
  background-color: var(--mdx-editor-background);
  border-bottom: 1px solid var(--mdx-editor-border);
  padding: 0.5rem;
  position: sticky;
  top: 0;
  z-index: 50;
  backdrop-filter: blur(8px);
}

.mdx-editor-toolbar-group {
  border-right: 1px solid var(--mdx-editor-border);
  padding-right: 0.5rem;
  margin-right: 0.5rem;
}

.mdx-editor-toolbar button {
  color: var(--mdx-editor-text);
}

.mdx-editor-toolbar button:hover {
  background-color: var(--mdx-editor-muted);
}

.mdx-editor-toolbar svg {
  color: var(--mdx-editor-text);
  stroke-width: 1.5px;
}

/* 编辑区域 */
.mdx-editor-content-editable {
  background-color: var(--mdx-editor-background);
  color: var(--mdx-editor-text);
  min-height: 300px;
  padding: 1rem;
}

/* 修复光标颜色问题 - 确保光标在所有情况下都可见 */
.mdx-editor-container .mdxeditor-root-contenteditable {
  caret-color: var(--mdx-editor-text);
}

/* 针对具体的编辑元素确保光标可见 */
.mdx-editor-container .mdxeditor-root-contenteditable *,
.mdx-editor-container .mdxeditor-root-contenteditable p,
.mdx-editor-container .mdxeditor-root-contenteditable div,
.mdx-editor-container .mdxeditor-root-contenteditable span {
  caret-color: var(--mdx-editor-text);
}

/* 确保在聚焦状态下光标可见 */
.mdx-editor-container .mdxeditor-root-contenteditable:focus {
  caret-color: var(--mdx-editor-primary);
  outline: none;
}

/* 确保在聚焦状态下所有子元素的光标可见 */
.mdx-editor-container .mdxeditor-root-contenteditable:focus *,
.mdx-editor-container .mdxeditor-root-contenteditable:focus p,
.mdx-editor-container .mdxeditor-root-contenteditable:focus div,
.mdx-editor-container .mdxeditor-root-contenteditable:focus span {
  caret-color: var(--mdx-editor-primary);
}

/* 文本选择颜色 */
.mdx-editor-container .mdxeditor-root-contenteditable::selection {
  background-color: var(--mdx-editor-primary);
  color: var(--mdx-editor-background);
}

.mdx-editor-container .mdxeditor-root-contenteditable *::selection {
  background-color: var(--mdx-editor-primary);
  color: var(--mdx-editor-background);
}

/* 变体最小高度 */
.mdx-editor-variant-article {
  min-height: 500px !important;
}

/* 标题样式 */
.mdx-editor-content-editable h1,
.mdx-editor-content-editable h2,
.mdx-editor-content-editable h3,
.mdx-editor-content-editable h4,
.mdx-editor-content-editable h5,
.mdx-editor-content-editable h6 {
  color: var(--mdx-editor-text);
}

/* 段落样式 */
.mdx-editor-content-editable p {
  color: var(--mdx-editor-text);
}

/* 链接样式 */
.mdx-editor-content-editable a {
  color: var(--mdx-editor-primary);
}

.mdx-editor-content-editable a:hover {
  color: var(--mdx-editor-primary-hover);
}

/* 代码块样式 */
.mdx-editor-content-editable code {
  background-color: var(--mdx-editor-muted);
  color: var(--mdx-editor-text);
  padding: 0.25rem;
  border-radius: 0.25rem;
}

/* 引用样式 */
.mdx-editor-content-editable blockquote {
  color: var(--mdx-editor-text);
  border-left: 4px solid var(--mdx-editor-primary);
  opacity: 0.8;
}

/* 列表样式 */
.mdx-editor-content-editable li {
  color: var(--mdx-editor-text);
}

/* 表格样式 */
.mdx-editor-content-editable table {
  border-collapse: collapse;
}

.mdx-editor-content-editable th,
.mdx-editor-content-editable td {
  border: 1px solid var(--mdx-editor-border);
  padding: 0.5rem;
}

/* 变体样式 */
.mdx-editor-variant-paragraph {
  font-size: 0.875rem;
}

.mdx-editor-variant-article {
  font-size: 1.125rem;
}

.mdx-editor-variant-default {
  font-size: 1rem;
}
